import psutil
import subprocess
import os
import time
from pynput import mouse
from pynput.keyboard import Key, Controller
import threading
import pyautogui
import cv2
import numpy as np
from PIL import Image
import win32gui
import win32con
import win32ui
from ctypes import windll

class SpotifyRestarter:
    def __init__(self):
        self.spotify_process_names = [
            'spotify.exe',
            'xboxgamebarspotify.exe',
            'spotifywebhelper.exe'
        ]
        self.running = True
        self.keyboard = Controller()
        self.auto_detection_enabled = True
        self.check_interval = 0.3  # Быстрая проверка
        
        # Настройка pyautogui
        pyautogui.FAILSAFE = False
        
    def find_spotify_window_handle(self):
        """Найти handle окна Spotify"""
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_title = win32gui.GetWindowText(hwnd)
                if 'spotify' in window_title.lower() and len(window_title) > 1:
                    windows.append((hwnd, window_title))
            return True
        
        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        
        if windows:
            # Возвращаем первое найденное окно Spotify
            return windows[0][0], windows[0][1]
        return None, None
    
    def capture_spotify_right_area_precise(self):
        """Точный захват правой части окна Spotify"""
        try:
            hwnd, title = self.find_spotify_window_handle()
            if not hwnd:
                print("Spotify window not found")
                return None, ""
            
            # Получаем размеры окна
            rect = win32gui.GetWindowRect(hwnd)
            left, top, right, bottom = rect
            width = right - left
            height = bottom - top
            
            # Проверяем, что окно не свернуто
            if width < 100 or height < 100:
                return None, title
            
            # Захватываем правую часть (примерно 40% от ширины)
            right_area_left = left + int(width * 0.6)
            right_area_width = int(width * 0.4)
            
            # Захватываем только верхнюю часть правой области (где now playing)
            capture_height = min(200, int(height * 0.3))
            
            # Делаем скриншот
            screenshot = pyautogui.screenshot(region=(
                right_area_left, 
                top + 50,  # Отступ от верха окна
                right_area_width, 
                capture_height
            ))
            
            return screenshot, title
            
        except Exception as e:
            print(f"Error capturing screen: {e}")
            return None, ""
    
    def preprocess_image_for_ocr(self, image):
        """Предобработка изображения для лучшего OCR"""
        try:
            # Конвертируем в numpy array
            img_array = np.array(image)
            
            # Конвертируем в оттенки серого
            gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
            
            # Увеличиваем контрастность
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
            enhanced = clahe.apply(gray)
            
            # Применяем пороговую обработку
            _, thresh = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            # Убираем шум
            kernel = np.ones((2,2), np.uint8)
            cleaned = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
            
            return cleaned
            
        except Exception as e:
            print(f"Error preprocessing image: {e}")
            return None
    
    def detect_advertisement_in_image(self, image):
        """Обнаружить рекламу в изображении"""
        if not image:
            return False
            
        try:
            # Предобрабатываем изображение
            processed_img = self.preprocess_image_for_ocr(image)
            if processed_img is None:
                return False
            
            # Извлекаем текст с улучшенными настройками
            custom_config = r'--oem 3 --psm 6 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789 '
            
            try:
                import pytesseract
                text = pytesseract.image_to_string(processed_img, config=custom_config).lower()
            except ImportError:
                print("Tesseract not available, using basic text detection")
                return False
            
            print(f"Detected text: '{text.strip()}'")
            
            # Расширенный список ключевых слов рекламы
            ad_keywords = [
                'advertisement',
                'ad',
                'sponsored',
                'premium',
                'upgrade',
                'listen without ads',
                'get premium',
                'try premium',
                'spotify premium',
                'ad break',
                'commercial',
                'skip ads',
                'no ads',
                'free trial'
            ]
            
            # Проверяем наличие ключевых слов
            text_clean = text.strip().replace('\n', ' ').replace('\r', '')
            for keyword in ad_keywords:
                if keyword in text_clean:
                    print(f"Advertisement keyword found: '{keyword}'")
                    return True
            
            # Проверяем подозрительные паттерны
            if len(text_clean) < 3 and len(text_clean) > 0:
                print(f"Suspicious short text: '{text_clean}'")
                return True
                
            return False
            
        except Exception as e:
            print(f"Error in advertisement detection: {e}")
            return False
    
    def kill_spotify(self):
        """Завершить все процессы Spotify"""
        killed = False
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                proc_name = proc.info['name'].lower()
                if proc_name in self.spotify_process_names:
                    proc.terminate()
                    killed = True
                    print(f"Terminated {proc.info['name']} (PID: {proc.info['pid']})")
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        
        if killed:
            time.sleep(0.5)
            
        return killed
    
    def start_spotify(self):
        """Запустить Spotify"""
        try:
            subprocess.run(['start', 'spotify:'], shell=True)
            print("Spotify started")
            return True
        except Exception as e:
            print(f"Error starting Spotify: {e}")
            return False
    
    def start_playback(self):
        """Start Spotify playback"""
        try:
            time.sleep(0.5)
            self.keyboard.press(Key.space)
            time.sleep(0.02)
            self.keyboard.release(Key.space)
            print("Playback started")
            return True
        except Exception as e:
            print(f"Failed to start playback: {e}")
            return False
    
    def restart_spotify(self):
        """Restart Spotify"""
        print(">> ADVERTISEMENT DETECTED - Restarting Spotify...")
        
        killed = self.kill_spotify()
        time.sleep(0.3)
        
        if self.start_spotify():
            time.sleep(2)
            self.start_playback()
            print(">> Restart complete!")
    
    def auto_detection_loop(self):
        """Основной цикл автоматического обнаружения рекламы"""
        print(">> Screen capture detection started...")
        
        while self.running:
            try:
                if self.auto_detection_enabled:
                    # Захватываем правую часть экрана Spotify
                    screenshot, title = self.capture_spotify_right_area_precise()
                    
                    if screenshot:
                        # Анализируем изображение на наличие рекламы
                        if self.detect_advertisement_in_image(screenshot):
                            # Обнаружена реклама - перезапускаем
                            threading.Thread(target=self.restart_spotify, daemon=True).start()
                            time.sleep(5)  # Пауза после перезапуска
                    else:
                        print("Could not capture Spotify window")
                
                time.sleep(self.check_interval)
                
            except Exception as e:
                print(f"Error in auto-detection: {e}")
                time.sleep(1)
    
    def on_click(self, x, y, button, pressed):
        """Mouse click handler"""
        if pressed and button == mouse.Button.middle:
            print(">> Manual restart triggered")
            threading.Thread(target=self.restart_spotify, daemon=True).start()
    
    def start_listener(self):
        """Start mouse event listener and auto-detection"""
        print("Spotify Screen Capture Auto-Restarter")
        print("=" * 45)
        print("Features:")
        print("  - Screen capture of Spotify right panel")
        print("  - OCR text recognition for ads")
        print("  - Automatic restart on ad detection")
        print("  - Manual restart with middle mouse button")
        print("=" * 45)
        print("Starting screen monitoring...")
        
        # Запускаем автоматическое обнаружение
        auto_thread = threading.Thread(target=self.auto_detection_loop, daemon=True)
        auto_thread.start()
        
        # Запускаем слушатель мыши
        with mouse.Listener(on_click=self.on_click) as listener:
            try:
                listener.join()
            except KeyboardInterrupt:
                print("\nScript stopped by user")
                self.running = False

def main():
    # Проверяем наличие tesseract
    try:
        import pytesseract
        pytesseract.get_tesseract_version()
        print("Tesseract OCR found - full functionality available")
    except:
        print("Warning: Tesseract OCR not found. Install from:")
        print("https://github.com/UB-Mannheim/tesseract/wiki")
        print("OCR detection will be disabled")
    
    restarter = SpotifyRestarter()
    restarter.start_listener()

if __name__ == "__main__":
    main()
