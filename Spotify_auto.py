import psutil
import subprocess
import os
import time
from pynput import mouse
from pynput.keyboard import Key, Controller
import threading
import pyautogui
import cv2
import numpy as np
from PIL import Image
import pytesseract

class SpotifyRestarter:
    def __init__(self):
        # Возможные имена процессов Spotify
        self.spotify_process_names = [
            'spotify.exe',
            'xboxgamebarspotify.exe',
            'spotifywebhelper.exe'
        ]
        self.running = True
        self.is_store_version = False
        self.keyboard = Controller()
        self.auto_detection_enabled = True
        self.check_interval = 0.5  # Минимальная задержка - 0.5 секунды
        
        # Настройка pyautogui
        pyautogui.FAILSAFE = False
        
    def find_spotify_window(self):
        """Найти окно Spotify"""
        try:
            # Попробуем найти окно Spotify
            windows = pyautogui.getWindowsWithTitle('Spotify')
            if not windows:
                windows = pyautogui.getWindowsWithTitle('spotify')
            
            if windows:
                return windows[0]
            return None
        except Exception as e:
            print(f"Error finding Spotify window: {e}")
            return None
    
    def capture_spotify_right_area(self):
        """Захватить только правую часть окна Spotify (Now Playing область)"""
        try:
            window = self.find_spotify_window()
            if not window:
                return None

            # Получаем размеры окна
            left, top, width, height = window.left, window.top, window.width, window.height

            # Проверяем минимальные размеры окна
            if width < 300 or height < 200:
                print("Spotify window too small")
                return None

            # Захватываем только правую часть окна (Now Playing область)
            # Начинаем с 75% ширины окна (более узкая область)
            right_area_left = left + int(width * 0.75)
            right_area_width = int(width * 0.25)  # Только 25% ширины

            # Ограничиваем высоту - только верхнюю часть правой области
            # где обычно показывается информация о треке
            capture_top = top + 60  # Отступ от верха (пропускаем заголовок)
            capture_height = min(150, int(height * 0.3))  # Максимум 150px высоты

            print(f"Capturing region: x={right_area_left}, y={capture_top}, w={right_area_width}, h={capture_height}")

            screenshot = pyautogui.screenshot(region=(
                right_area_left,
                capture_top,
                right_area_width,
                capture_height
            ))
            return screenshot
        except Exception as e:
            print(f"Error capturing screen: {e}")
            return None
    
    def detect_advertisement(self, image):
        """Обнаружить рекламу в изображении"""
        if not image:
            return False
            
        try:
            # Конвертируем в формат для OCR
            image_cv = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
            
            # Извлекаем текст
            text = pytesseract.image_to_string(image_cv, lang='eng').lower()
            
            # Ищем ключевые слова рекламы
            ad_keywords = [
                'advertisement',
                'ad',
                'sponsored',
                'premium',
                'upgrade',
                'listen without ads',
                'get premium'
            ]
            
            for keyword in ad_keywords:
                if keyword in text:
                    print(f"Advertisement detected: '{keyword}' found in text")
                    return True
                    
            return False
        except Exception as e:
            print(f"Error in OCR: {e}")
            return False
    
    def kill_spotify(self):
        """Завершить все процессы Spotify"""
        killed = False
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                proc_name = proc.info['name'].lower()
                if proc_name in self.spotify_process_names:
                    proc.terminate()
                    killed = True
                    print(f"Terminated process {proc.info['name']} (PID: {proc.info['pid']})")
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        
        if killed:
            time.sleep(1)  # Минимальная задержка
            
        return killed
    
    def start_spotify(self):
        """Запустить Spotify"""
        try:
            # Для Microsoft Store версии используем протокол
            subprocess.run(['start', 'spotify:'], shell=True)
            print("Spotify started")
            return True
        except Exception as e:
            print(f"Error starting Spotify: {e}")
            return False
    
    def start_playback(self):
        """Start Spotify playback"""
        print("Starting playback...")
        try:
            time.sleep(1)  # Минимальная задержка
            
            # Send spacebar key
            self.keyboard.press(Key.space)
            time.sleep(0.05)  # Очень короткая задержка
            self.keyboard.release(Key.space)
            print("Playback started")
            return True
        except Exception as e:
            print(f"Failed to start playback: {e}")
            return False
    
    def restart_spotify(self):
        """Restart Spotify"""
        print("Restarting Spotify due to advertisement...")
        
        killed = self.kill_spotify()
        
        if not killed:
            print("Spotify processes not found")
        
        time.sleep(0.3)  # Минимальная задержка
        if self.start_spotify():
            time.sleep(2)  # Минимальное время загрузки
            self.start_playback()
    
    def auto_detection_loop(self):
        """Основной цикл автоматического обнаружения рекламы"""
        print("Auto-detection started. Checking for advertisements...")
        
        while self.running:
            try:
                if self.auto_detection_enabled:
                    # Захватываем правую часть экрана Spotify
                    screenshot = self.capture_spotify_right_area()
                    
                    if screenshot and self.detect_advertisement(screenshot):
                        # Обнаружена реклама - перезапускаем
                        threading.Thread(target=self.restart_spotify, daemon=True).start()
                        # Пауза после перезапуска
                        time.sleep(5)
                
                time.sleep(self.check_interval)
                
            except Exception as e:
                print(f"Error in auto-detection loop: {e}")
                time.sleep(1)
    
    def on_click(self, x, y, button, pressed):
        """Mouse click handler"""
        if pressed and button == mouse.Button.middle:
            print("Middle mouse button pressed - manual restart")
            threading.Thread(target=self.restart_spotify, daemon=True).start()
    
    def start_listener(self):
        """Start mouse event listener and auto-detection"""
        print("Spotify Auto-Restarter started!")
        print("Features:")
        print("- Automatic advertisement detection and restart")
        print("- Manual restart with middle mouse button")
        print("- Minimal delays for fastest operation")
        print("Press Ctrl+C to exit")
        
        # Запускаем автоматическое обнаружение в отдельном потоке
        auto_thread = threading.Thread(target=self.auto_detection_loop, daemon=True)
        auto_thread.start()
        
        # Запускаем слушатель мыши
        with mouse.Listener(on_click=self.on_click) as listener:
            try:
                listener.join()
            except KeyboardInterrupt:
                print("\nScript stopped by user")
                self.running = False

def main():
    # Проверяем наличие tesseract
    try:
        pytesseract.get_tesseract_version()
    except:
        print("Warning: Tesseract OCR not found. Auto-detection may not work.")
        print("Install Tesseract from: https://github.com/UB-Mannheim/tesseract/wiki")
    
    restarter = SpotifyRestarter()
    restarter.start_listener()

if __name__ == "__main__":
    main()
