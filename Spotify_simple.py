import psutil
import subprocess
import time
from pynput import mouse
from pynput.keyboard import Key, Controller
import threading

class SimpleSpotifyRestarter:
    def __init__(self):
        self.spotify_process_names = [
            'spotify.exe',
            'xboxgamebarspotify.exe',
            'spotifywebhelper.exe'
        ]
        self.keyboard = Controller()
        
    def kill_spotify(self):
        """Завершить все процессы Spotify"""
        killed = False
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                proc_name = proc.info['name'].lower()
                if proc_name in self.spotify_process_names:
                    proc.terminate()
                    killed = True
                    print(f"Terminated {proc.info['name']}")
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        
        if killed:
            time.sleep(0.5)
        return killed
    
    def start_spotify(self):
        """Запустить Spotify"""
        try:
            subprocess.run(['start', 'spotify:'], shell=True)
            print("Spotify started")
            return True
        except Exception as e:
            print(f"Error starting Spotify: {e}")
            return False
    
    def start_playback(self):
        """Включить воспроизведение трека"""
        try:
            print("Starting playback...")
            
            # Метод 1: Медиа клавиша (работает глобально)
            self.keyboard.press(Key.media_play_pause)
            time.sleep(0.05)
            self.keyboard.release(Key.media_play_pause)
            time.sleep(0.5)
            
            # Метод 2: Пробел (на случай если медиа клавиша не сработала)
            self.keyboard.press(Key.space)
            time.sleep(0.05)
            self.keyboard.release(Key.space)
            
            print("Playback commands sent")
            return True
        except Exception as e:
            print(f"Failed to start playback: {e}")
            return False
    
    def restart_spotify(self):
        """Перезапустить Spotify и включить музыку"""
        print(">> Restarting Spotify...")
        
        # Шаг 1: Завершить Spotify
        self.kill_spotify()
        time.sleep(0.5)
        
        # Шаг 2: Запустить Spotify
        if self.start_spotify():
            print("Waiting for Spotify to load...")
            time.sleep(3.0)  # Ждем загрузки
            
            # Шаг 3: Включить воспроизведение
            self.start_playback()
            print(">> Restart complete!")
        else:
            print(">> Failed to start Spotify")
    
    def on_click(self, x, y, button, pressed):
        """Обработчик клика мыши"""
        if pressed and button == mouse.Button.middle:
            print(">> Manual restart triggered")
            threading.Thread(target=self.restart_spotify, daemon=True).start()
    
    def start_listener(self):
        """Запустить слушатель мыши"""
        print("Simple Spotify Restarter")
        print("=" * 30)
        print("Functions:")
        print("  - Restart Spotify")
        print("  - Start playback")
        print("=" * 30)
        print("Controls:")
        print("  - Middle mouse button: Manual restart")
        print("  - Ctrl+C: Exit")
        print("=" * 30)
        print("Ready! Press middle mouse button to test...")
        
        # Запускаем слушатель мыши
        with mouse.Listener(on_click=self.on_click) as listener:
            try:
                listener.join()
            except KeyboardInterrupt:
                print("\nStopped by user")

def main():
    restarter = SimpleSpotifyRestarter()
    restarter.start_listener()

if __name__ == "__main__":
    main()
