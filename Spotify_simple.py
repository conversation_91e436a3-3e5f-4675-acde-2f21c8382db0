import psutil
import subprocess
import os
import time
from pynput import mouse
from pynput.keyboard import Key, Controller
import threading

class SpotifyRestarter:
    def __init__(self):
        # Возможные имена процессов Spotify
        self.spotify_process_names = [
            'spotify.exe',
            'xboxgamebarspotify.exe',
            'spotifywebhelper.exe'
        ]
        self.running = True
        self.is_store_version = False
        self.keyboard = Controller()
        
    def find_spotify_executable(self):
        """Найти исполняемый файл Spotify"""
        # Сначала проверим, запущен ли Spotify из Microsoft Store
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                if proc.info['name'].lower() in ['xboxgamebarspotify.exe', 'spotify.exe']:
                    if proc.info['name'].lower() == 'xboxgamebarspotify.exe':
                        self.is_store_version = True
                        return "spotify:"  # Протокол для запуска Store версии
                    else:
                        self.is_store_version = False
                        return proc.exe()
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        
        # Если ничего не найдено, предполагаем Store версию
        self.is_store_version = True
        return "spotify:"
    
    def kill_spotify(self):
        """Завершить все процессы Spotify"""
        killed = False
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                proc_name = proc.info['name'].lower()
                if proc_name in self.spotify_process_names:
                    proc.terminate()
                    killed = True
                    print(f"Terminated process {proc.info['name']} (PID: {proc.info['pid']})")
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        
        if killed:
            # Ждем немного, чтобы процессы корректно завершились
            time.sleep(2)
            
        return killed
    
    def start_spotify(self):
        """Запустить Spotify"""
        spotify_path = self.find_spotify_executable()
        
        if spotify_path:
            try:
                if self.is_store_version or spotify_path == "spotify:":
                    # Для Microsoft Store версии используем протокол
                    subprocess.run(['start', 'spotify:'], shell=True)
                    print("Spotify started (Microsoft Store version)")
                else:
                    # Для обычной версии запускаем исполняемый файл
                    subprocess.Popen([spotify_path], shell=True)
                    print(f"Spotify started: {spotify_path}")
                return True
            except Exception as e:
                print(f"Error starting Spotify: {e}")
                return False
        else:
            print("Could not find Spotify executable")
            return False
    
    def start_playback(self):
        """Start Spotify playback using keyboard simulation"""
        print("Starting playback...")
        try:
            # Wait a bit more for Spotify to fully load
            time.sleep(2)
            
            # Send spacebar key (universal play/pause)
            self.keyboard.press(Key.space)
            time.sleep(0.1)
            self.keyboard.release(Key.space)
            print("Sent spacebar key to start playback")
            return True
        except Exception as e:
            print(f"Failed to start playback: {e}")
            return False
    
    def restart_spotify(self):
        """Restart Spotify"""
        print("Restarting Spotify...")
        
        # Kill process
        killed = self.kill_spotify()
        
        if not killed:
            print("Spotify processes not found")
        
        # Start again
        time.sleep(0.5)  # Small pause
        if self.start_spotify():
            # Wait for Spotify to fully load, then start playback
            print("Waiting for Spotify to load...")
            time.sleep(4)  # Give Spotify time to fully start
            self.start_playback()
    
    def on_click(self, x, y, button, pressed):
        """Mouse click handler"""
        if pressed and button == mouse.Button.middle:
            print("Middle mouse button pressed - restarting Spotify")
            # Run restart in separate thread to not block listener
            threading.Thread(target=self.restart_spotify, daemon=True).start()
    
    def start_listener(self):
        """Start mouse event listener"""
        print("Script started. Press middle mouse button to restart Spotify.")
        print("After restart, playback will start automatically.")
        print("Press Ctrl+C to exit")
        
        with mouse.Listener(on_click=self.on_click) as listener:
            try:
                listener.join()
            except KeyboardInterrupt:
                print("\nScript stopped by user")
                self.running = False

def main():
    restarter = SpotifyRestarter()
    restarter.start_listener()

if __name__ == "__main__":
    main()
