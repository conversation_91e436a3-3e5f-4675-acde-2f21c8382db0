import psutil
import subprocess
import os
import time

def test_spotify_detection():
    print("Testing Spotify detection...")
    
    # Check running processes
    spotify_processes = []
    for proc in psutil.process_iter(['pid', 'name']):
        try:
            proc_name = proc.info['name'].lower()
            if 'spotify' in proc_name:
                spotify_processes.append((proc.info['name'], proc.info['pid']))
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            pass
    
    if spotify_processes:
        print("Found Spotify processes:")
        for name, pid in spotify_processes:
            print(f"  - {name} (PID: {pid})")
    else:
        print("No Spotify processes found")
    
    # Test restart function
    print("\nTesting restart...")
    
    # Kill Spotify
    killed = False
    for proc in psutil.process_iter(['pid', 'name']):
        try:
            proc_name = proc.info['name'].lower()
            if 'spotify' in proc_name:
                proc.terminate()
                killed = True
                print(f"Terminated {proc.info['name']} (PID: {proc.info['pid']})")
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            pass
    
    if killed:
        print("Waiting 2 seconds...")
        time.sleep(2)
        
        # Start Spotify
        try:
            subprocess.run(['start', 'spotify:'], shell=True)
            print("Spotify started using protocol")
        except Exception as e:
            print(f"Error starting Spotify: {e}")
    else:
        print("No Spotify processes to restart")

if __name__ == "__main__":
    test_spotify_detection()
