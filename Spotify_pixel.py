import psutil
import subprocess
import os
import time
from pynput import mouse
from pynput.keyboard import Key, Controller
import threading
import pyautogui
import win32gui
import numpy as np
from PIL import Image

class SpotifyRestarter:
    def __init__(self):
        self.spotify_process_names = [
            'spotify.exe',
            'xboxgamebarspotify.exe',
            'spotifywebhelper.exe'
        ]
        self.running = True
        self.keyboard = Controller()
        self.auto_detection_enabled = True
        self.check_interval = 0.2  # Очень быстрая проверка
        self.last_screenshot_hash = None
        
        # Настройка pyautogui
        pyautogui.FAILSAFE = False
        
    def find_spotify_window_handle(self):
        """Найти handle окна Spotify"""
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_title = win32gui.GetWindowText(hwnd)
                if 'spotify' in window_title.lower() and len(window_title) > 1:
                    windows.append((hwnd, window_title))
            return True
        
        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        
        if windows:
            return windows[0][0], windows[0][1]
        return None, None
    
    def capture_spotify_now_playing_area(self):
        """Захват только правой части Now Playing области в Spotify"""
        try:
            hwnd, title = self.find_spotify_window_handle()
            if not hwnd:
                return None, ""

            # Получаем размеры окна
            rect = win32gui.GetWindowRect(hwnd)
            left, top, right, bottom = rect
            width = right - left
            height = bottom - top

            # Проверяем минимальные размеры окна
            if width < 400 or height < 300:
                return None, title

            # Захватываем ТОЛЬКО правую верхнюю часть окна
            # где показывается информация о текущем треке

            # Правая часть - начинаем с 70% ширины окна
            right_area_left = left + int(width * 0.7)
            right_area_width = int(width * 0.3)  # Только 30% ширины

            # Верхняя часть - пропускаем заголовок окна
            capture_top = top + 80  # Отступ от верха окна
            capture_height = 120    # Фиксированная высота для Now Playing

            # Дополнительная проверка - не выходим за границы окна
            if capture_top + capture_height > bottom - 100:
                capture_height = max(60, bottom - capture_top - 100)

            print(f"Capturing NOW PLAYING area: x={right_area_left}, y={capture_top}, w={right_area_width}, h={capture_height}")

            screenshot = pyautogui.screenshot(region=(
                right_area_left,
                capture_top,
                right_area_width,
                capture_height
            ))

            return screenshot, title

        except Exception as e:
            print(f"Error capturing now playing area: {e}")
            return None, ""
    
    def analyze_image_for_ads(self, image):
        """Анализ изображения на наличие рекламы по цветам и паттернам"""
        if not image:
            return False
            
        try:
            # Конвертируем в numpy array
            img_array = np.array(image)
            
            # Получаем средние значения цветов
            avg_colors = np.mean(img_array, axis=(0, 1))
            
            # Проверяем на характерные цвета рекламы Spotify
            # Реклама часто имеет более яркие/контрастные цвета
            brightness = np.mean(avg_colors)
            
            # Анализируем распределение цветов
            red_channel = img_array[:, :, 0]
            green_channel = img_array[:, :, 1]
            blue_channel = img_array[:, :, 2]
            
            # Проверяем на высокий контраст (характерно для рекламы)
            red_std = np.std(red_channel)
            green_std = np.std(green_channel)
            blue_std = np.std(blue_channel)
            
            total_contrast = red_std + green_std + blue_std
            
            # Если контрастность очень высокая, возможно это реклама
            if total_contrast > 150:  # Экспериментальное значение
                print(f"High contrast detected: {total_contrast:.1f} (possible ad)")
                return True
            
            # Проверяем на преобладание определенных цветов (зеленый Spotify)
            if avg_colors[1] > avg_colors[0] + 30 and avg_colors[1] > avg_colors[2] + 30:
                print("Spotify green color detected (possible premium ad)")
                return True
            
            # Создаем хеш изображения для отслеживания изменений
            img_hash = hash(img_array.tobytes())
            
            # Если изображение кардинально изменилось, возможно началась реклама
            if (self.last_screenshot_hash and 
                self.last_screenshot_hash != img_hash):
                
                # Дополнительная проверка - если изображение стало более "пестрым"
                if total_contrast > 100:
                    print("Sudden image change with high contrast (possible ad)")
                    self.last_screenshot_hash = img_hash
                    return True
            
            self.last_screenshot_hash = img_hash
            return False
            
        except Exception as e:
            print(f"Error analyzing image: {e}")
            return False
    
    def detect_ad_by_window_changes(self, title):
        """Дополнительная проверка по изменениям заголовка"""
        if not title:
            return False
            
        title_lower = title.lower()
        
        # Простые индикаторы рекламы
        ad_words = ['ad', 'advertisement', 'premium', 'upgrade', 'sponsored']
        
        for word in ad_words:
            if word in title_lower:
                print(f"Ad keyword in title: {word}")
                return True
                
        return False
    
    def kill_spotify(self):
        """Завершить все процессы Spotify"""
        killed = False
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                proc_name = proc.info['name'].lower()
                if proc_name in self.spotify_process_names:
                    proc.terminate()
                    killed = True
                    print(f"Terminated {proc.info['name']}")
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        
        if killed:
            time.sleep(0.3)
            
        return killed
    
    def start_spotify(self):
        """Запустить Spotify"""
        try:
            subprocess.run(['start', 'spotify:'], shell=True)
            print("Spotify started")
            return True
        except Exception as e:
            print(f"Error starting Spotify: {e}")
            return False
    
    def start_playback(self):
        """Start Spotify playback"""
        try:
            time.sleep(0.5)
            self.keyboard.press(Key.space)
            time.sleep(0.02)
            self.keyboard.release(Key.space)
            print("Playback started")
            return True
        except Exception as e:
            print(f"Failed to start playback: {e}")
            return False
    
    def restart_spotify(self):
        """Restart Spotify"""
        print(">> AD DETECTED - Restarting Spotify...")
        
        self.kill_spotify()
        time.sleep(0.2)
        
        if self.start_spotify():
            time.sleep(1.5)
            self.start_playback()
            print(">> Restart complete!")
    
    def auto_detection_loop(self):
        """Основной цикл обнаружения рекламы"""
        print(">> Pixel analysis detection started...")
        
        while self.running:
            try:
                if self.auto_detection_enabled:
                    # Захватываем область плеера
                    screenshot, title = self.capture_spotify_now_playing_area()
                    
                    ad_detected = False
                    
                    # Проверяем заголовок
                    if self.detect_ad_by_window_changes(title):
                        ad_detected = True
                    
                    # Анализируем изображение
                    if screenshot and self.analyze_image_for_ads(screenshot):
                        ad_detected = True
                    
                    if ad_detected:
                        threading.Thread(target=self.restart_spotify, daemon=True).start()
                        time.sleep(3)  # Пауза после перезапуска
                
                time.sleep(self.check_interval)
                
            except Exception as e:
                print(f"Error in detection loop: {e}")
                time.sleep(0.5)
    
    def on_click(self, x, y, button, pressed):
        """Mouse click handler"""
        if pressed and button == mouse.Button.middle:
            print(">> Manual restart")
            threading.Thread(target=self.restart_spotify, daemon=True).start()
    
    def start_listener(self):
        """Start detection and mouse listener"""
        print("Spotify Pixel Analysis Auto-Restarter")
        print("=" * 40)
        print("Features:")
        print("  - Fast pixel analysis (200ms)")
        print("  - Color pattern detection")
        print("  - Window title monitoring")
        print("  - No OCR dependencies")
        print("=" * 40)
        print("Starting detection...")
        
        # Запускаем обнаружение
        auto_thread = threading.Thread(target=self.auto_detection_loop, daemon=True)
        auto_thread.start()
        
        # Запускаем слушатель мыши
        with mouse.Listener(on_click=self.on_click) as listener:
            try:
                listener.join()
            except KeyboardInterrupt:
                print("\nStopped by user")
                self.running = False

def main():
    restarter = SpotifyRestarter()
    restarter.start_listener()

if __name__ == "__main__":
    main()
