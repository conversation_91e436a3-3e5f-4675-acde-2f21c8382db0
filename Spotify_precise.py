import psutil
import subprocess
import os
import time
from pynput import mouse
from pynput.keyboard import Key, Controller
import threading
import pyautogui
import win32gui
import numpy as np
from PIL import Image

class SpotifyRestarter:
    def __init__(self):
        self.spotify_process_names = [
            'spotify.exe',
            'xboxgamebarspotify.exe',
            'spotifywebhelper.exe'
        ]
        self.running = True
        self.keyboard = Controller()
        self.auto_detection_enabled = True
        self.check_interval = 0.3  # Быстрая проверка
        self.last_title = ""
        
        # Настройка pyautogui
        pyautogui.FAILSAFE = False
        
    def find_spotify_window_handle(self):
        """Найти handle окна Spotify"""
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_title = win32gui.GetWindowText(hwnd)
                if 'spotify' in window_title.lower() and len(window_title) > 1:
                    windows.append((hwnd, window_title))
            return True
        
        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        
        if windows:
            return windows[0][0], windows[0][1]
        return None, None
    
    def capture_track_info_area_only(self):
        """Захват правой панели Spotify с информацией об исполнителе"""
        try:
            hwnd, title = self.find_spotify_window_handle()
            if not hwnd:
                return None, ""

            # Получаем размеры окна
            rect = win32gui.GetWindowRect(hwnd)
            left, top, right, bottom = rect
            width = right - left
            height = bottom - top

            # Проверяем минимальные размеры окна
            if width < 800 or height < 600:
                print("Spotify window too small for right panel capture")
                return None, title

            # ЗАХВАТ ПРАВОЙ ПАНЕЛИ - как на скриншоте
            # Правая панель начинается примерно с 75% ширины окна
            right_panel_left = left + int(width * 0.75)
            right_panel_width = int(width * 0.25)  # 25% ширины для правой панели

            # Захватываем всю высоту правой панели, исключая заголовок и нижнюю панель
            panel_top = top + 40    # Небольшой отступ от верха (заголовок окна)
            panel_height = height - 140  # Исключаем нижнюю панель управления

            # Убеждаемся что не выходим за границы
            if right_panel_left + right_panel_width > right:
                right_panel_width = right - right_panel_left - 5

            if panel_top + panel_height > bottom:
                panel_height = bottom - panel_top - 40

            # Минимальные размеры для корректной работы
            if right_panel_width < 100 or panel_height < 200:
                print("Right panel area too small")
                return None, title

            print(f"Right panel capture: x={right_panel_left}, y={panel_top}, w={right_panel_width}, h={panel_height}")

            screenshot = pyautogui.screenshot(region=(
                right_panel_left,
                panel_top,
                right_panel_width,
                panel_height
            ))

            return screenshot, title

        except Exception as e:
            print(f"Error capturing right panel: {e}")
            return None, ""
    
    def detect_ad_by_title_change(self, title):
        """Обнаружение рекламы по изменению заголовка"""
        if not title:
            return False
            
        title_clean = title.strip()
        
        # Прямые индикаторы рекламы в заголовке
        ad_indicators = [
            'advertisement',
            'spotify ad',
            'premium',
            'upgrade',
            'listen without ads',
            'get premium',
            'ad break'
        ]
        
        title_lower = title_clean.lower()
        for indicator in ad_indicators:
            if indicator in title_lower:
                print(f"AD DETECTED in title: '{indicator}' found in '{title}'")
                return True
        
        # Проверка на подозрительные изменения
        if self.last_title and title_clean != self.last_title:
            # Если заголовок стал просто "Spotify" (часто во время рекламы)
            if title_clean.lower() == 'spotify':
                print(f"AD DETECTED: Title changed to 'Spotify' from '{self.last_title}'")
                self.last_title = title_clean
                return True
            
            # Если заголовок стал очень коротким
            if len(title_clean) < 5 and len(self.last_title) > 10:
                print(f"AD DETECTED: Title became very short: '{title_clean}'")
                self.last_title = title_clean
                return True
        
        self.last_title = title_clean
        return False
    
    def analyze_track_area_colors(self, image):
        """Анализ цветов в области трека для обнаружения рекламы"""
        if not image:
            return False
            
        try:
            # Конвертируем в numpy array
            img_array = np.array(image)
            
            # Анализируем цветовую схему
            avg_colors = np.mean(img_array, axis=(0, 1))
            
            # Проверяем на характерные цвета Spotify Premium (зеленый)
            green_dominance = avg_colors[1] - max(avg_colors[0], avg_colors[2])
            
            if green_dominance > 40:  # Сильное преобладание зеленого
                print(f"AD DETECTED: Strong green color detected (Premium ad)")
                return True
            
            # Проверяем на высокую яркость (характерно для рекламы)
            brightness = np.mean(avg_colors)
            if brightness > 200:  # Очень яркое изображение
                print(f"AD DETECTED: Very bright colors detected: {brightness:.1f}")
                return True
            
            # Проверяем контрастность
            std_colors = np.std(img_array, axis=(0, 1))
            total_contrast = np.sum(std_colors)
            
            if total_contrast > 120:  # Высокий контраст
                print(f"AD DETECTED: High contrast detected: {total_contrast:.1f}")
                return True
            
            return False
            
        except Exception as e:
            print(f"Error analyzing colors: {e}")
            return False
    
    def kill_spotify(self):
        """Завершить все процессы Spotify"""
        killed = False
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                proc_name = proc.info['name'].lower()
                if proc_name in self.spotify_process_names:
                    proc.terminate()
                    killed = True
                    print(f"Terminated {proc.info['name']}")
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        
        if killed:
            time.sleep(0.3)
        return killed
    
    def start_spotify(self):
        """Запустить Spotify"""
        try:
            subprocess.run(['start', 'spotify:'], shell=True)
            print("Spotify started")
            return True
        except Exception as e:
            print(f"Error starting Spotify: {e}")
            return False
    
    def start_playback(self):
        """Start Spotify playback"""
        try:
            time.sleep(0.5)
            self.keyboard.press(Key.space)
            time.sleep(0.02)
            self.keyboard.release(Key.space)
            print("Playback started")
            return True
        except Exception as e:
            print(f"Failed to start playback: {e}")
            return False
    
    def restart_spotify(self):
        """Restart Spotify"""
        print(">> ADVERTISEMENT DETECTED - Restarting Spotify...")

        self.kill_spotify()
        time.sleep(0.2)

        if self.start_spotify():
            time.sleep(1.5)
            self.start_playback()
            print(">> Restart complete!")
    
    def auto_detection_loop(self):
        """Основной цикл обнаружения рекламы"""
        print(">> Precise track area monitoring started...")
        
        while self.running:
            try:
                if self.auto_detection_enabled:
                    # Захватываем только область с информацией о треке
                    screenshot, title = self.capture_track_info_area_only()
                    
                    ad_detected = False
                    
                    # Проверка 1: Анализ заголовка окна
                    if self.detect_ad_by_title_change(title):
                        ad_detected = True
                    
                    # Проверка 2: Анализ цветов в области трека
                    if screenshot and self.analyze_track_area_colors(screenshot):
                        ad_detected = True
                    
                    if ad_detected:
                        threading.Thread(target=self.restart_spotify, daemon=True).start()
                        time.sleep(4)  # Пауза после перезапуска
                
                time.sleep(self.check_interval)
                
            except Exception as e:
                print(f"Error in detection loop: {e}")
                time.sleep(0.5)
    
    def on_click(self, x, y, button, pressed):
        """Mouse click handler"""
        if pressed and button == mouse.Button.middle:
            print(">> Manual restart")
            threading.Thread(target=self.restart_spotify, daemon=True).start()
    
    def start_listener(self):
        """Start detection and mouse listener"""
        print("Spotify PRECISE Area Auto-Restarter")
        print("=" * 45)
        print("Features:")
        print("  - MINIMAL capture area (track info only)")
        print("  - Precise color analysis")
        print("  - Title change detection")
        print("  - Avoids false positives from banners")
        print("=" * 45)
        print("Starting precise monitoring...")
        
        # Запускаем обнаружение
        auto_thread = threading.Thread(target=self.auto_detection_loop, daemon=True)
        auto_thread.start()
        
        # Запускаем слушатель мыши
        with mouse.Listener(on_click=self.on_click) as listener:
            try:
                listener.join()
            except KeyboardInterrupt:
                print("\nStopped by user")
                self.running = False

def main():
    restarter = SpotifyRestarter()
    restarter.start_listener()

if __name__ == "__main__":
    main()
