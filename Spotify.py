import psutil
import subprocess
import os
import time
from pynput import mouse
import threading

class SpotifyRestarter:
    def __init__(self):
        # Возможные имена процессов Spotify
        self.spotify_process_names = [
            'spotify.exe',
            'xboxgamebarspotify.exe',
            'spotifywebhelper.exe'
        ]
        # Возможные пути к исполняемым файлам
        self.spotify_paths = [
            # Обычная версия Spotify
            os.path.expanduser("~\\AppData\\Roaming\\Spotify\\Spotify.exe"),
            "C:\\Users\\<USER>\\AppData\\Roaming\\Spotify\\Spotify.exe",
            "C:\\Program Files\\Spotify\\Spotify.exe",
            "C:\\Program Files (x86)\\Spotify\\Spotify.exe",
            # Microsoft Store версия (запускается через протокол)
            "spotify:"
        ]
        self.running = True
        self.is_store_version = False

    def find_spotify_executable(self):
        """Найти исполняемый файл Spotify"""
        for path in self.spotify_paths:
            expanded_path = os.path.expandvars(path)
            if os.path.exists(expanded_path):
                return expanded_path

        # Попробуем найти через реестр или PATH
        try:
            result = subprocess.run(['where', 'spotify'],
                                  capture_output=True, text=True, shell=True)
            if result.returncode == 0:
                return result.stdout.strip().split('\n')[0]
        except:
            pass

        return None

    def kill_spotify(self):
        """Завершить все процессы Spotify"""
        killed = False
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                if proc.info['name'].lower() == 'spotify.exe':
                    proc.terminate()
                    killed = True
                    print(f"Завершен процесс Spotify (PID: {proc.info['pid']})")
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass

        if killed:
            # Ждем немного, чтобы процессы корректно завершились
            time.sleep(1)

        return killed

    def start_spotify(self):
        """Запустить Spotify"""
        spotify_path = self.find_spotify_executable()

        if spotify_path:
            try:
                subprocess.Popen([spotify_path], shell=True)
                print(f"Spotify запущен: {spotify_path}")
                return True
            except Exception as e:
                print(f"Ошибка при запуске Spotify: {e}")
                return False
        else:
            print("Не удалось найти исполняемый файл Spotify")
            return False

    def restart_spotify(self):
        """Перезапустить Spotify"""
        print("Перезапуск Spotify...")

        # Завершаем процесс
        killed = self.kill_spotify()

        if not killed:
            print("Процессы Spotify не найдены")

        # Запускаем заново
        time.sleep(0.5)  # Небольшая пауза
        self.start_spotify()

    def on_click(self, x, y, button, pressed):
        """Обработчик нажатий мыши"""
        if pressed and button == mouse.Button.middle:
            print("Нажата средняя кнопка мыши - перезапуск Spotify")
            # Запускаем перезапуск в отдельном потоке, чтобы не блокировать listener
            threading.Thread(target=self.restart_spotify, daemon=True).start()

    def start_listener(self):
        """Запустить прослушивание событий мыши"""
        print("Скрипт запущен. Нажмите среднюю кнопку мыши для перезапуска Spotify.")
        print("Для выхода нажмите Ctrl+C")

        with mouse.Listener(on_click=self.on_click) as listener:
            try:
                listener.join()
            except KeyboardInterrupt:
                print("\nСкрипт остановлен пользователем")
                self.running = False

def main():
    restarter = SpotifyRestarter()
    restarter.start_listener()

if __name__ == "__main__":
    main()