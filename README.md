# Spotify Auto-Restarter

Продвинутый скрипт для автоматического перезапуска Spotify с обнаружением рекламы и мгновенным запуском воспроизведения.

## 🚀 Версии скриптов

### 1. **Spotify_precise.py** (🎯 РЕКОМЕНДУЕТСЯ)
- 🎯 **ТОЧНЫЙ захват только области трека** (избегает баннеры)
- ⚡ **Быстрое обнаружение рекламы** (300мс)
- 🎨 **Анализ цветов и контрастности**
- 📊 **Мониторинг заголовка окна**
- 🚫 **Минимум ложных срабатываний**

### 2. **Spotify_fast.py** (Быстрая версия)
- ⚡ **Ультра-быстрое обнаружение** (200мс)
- 📊 **Мониторинг заголовка окна**
- ⚡ **Минимальные задержки**

### 3. **Spotify_pixel.py** (Анализ пикселей)
- 🎨 **Анализ цветов и паттернов**
- 📸 **Захват области плеера**
- 🔍 **Обнаружение по изменениям**

### 4. **Spotify_screen.py** (OCR версия)
- 🔍 **OCR распознавание текста**
- 📸 **Анализ скриншотов**
- 🎯 **Продвинутое обнаружение рекламы**

### 5. **Spotify_simple.py** (Базовая версия)
- 🖱️ **Только ручной перезапуск**
- 🎵 **Автозапуск воспроизведения**
- 📦 **Минимум зависимостей**

## ✨ Основные функции

- 🔄 Завершает все процессы Spotify
- 🚀 Автоматически запускает Spotify заново
- 🎵 **НОВОЕ**: Автоматически начинает воспроизведение
- 🤖 **НОВОЕ**: Обнаруживает рекламу и перезапускается автоматически
- 🖱️ Ручной перезапуск средней кнопкой мыши
- ⚡ Работает с минимальными задержками

## 🚀 Быстрый запуск

### Рекомендуемый способ (Быстрая версия):
```bash
# Установка зависимостей
pip install -r requirements_fast.txt

# Запуск быстрой версии
python Spotify_fast.py
```

### Или используйте batch файл:
```bash
run_fast.bat
```

### Альтернативные версии:
```bash
# Простая версия (только ручной перезапуск)
python Spotify_simple.py

# Полная версия с OCR (требует Tesseract)
pip install -r requirements.txt
python Spotify_auto.py
```

## 🎮 Использование

### Автоматический режим (Spotify_fast.py):
1. Запустите скрипт
2. **Скрипт автоматически обнаруживает рекламу каждые 200мс**
3. При обнаружении рекламы - автоматический перезапуск
4. Музыка начинает играть автоматически после перезапуска

### Ручной режим:
1. Нажмите **среднюю кнопку мыши** (колесико) в любом месте экрана
2. Spotify мгновенно перезапустится
3. Воспроизведение начнется автоматически

### Выход:
- Нажмите **Ctrl+C** для остановки скрипта

## Требования

- Python 3.6+
- Windows
- Установленный Spotify

## Зависимости

- `psutil` - для управления процессами
- `pynput` - для отслеживания нажатий мыши

## 🔧 Как работает обнаружение рекламы

### Spotify_fast.py (Рекомендуется):
- **Мониторинг заголовка окна**: Проверяет заголовок окна Spotify каждые 200мс
- **Ключевые слова**: Ищет слова "advertisement", "ad", "premium", "sponsored" и др.
- **Изменения заголовка**: Обнаруживает подозрительные изменения в названии
- **Минимальные задержки**: Перезапуск за 2-3 секунды

### Spotify_auto.py (Продвинутая):
- **OCR распознавание**: Анализирует текст на экране
- **Скриншоты**: Захватывает правую часть окна Spotify
- **Глубокий анализ**: Более точное, но медленное обнаружение

## 📝 Примечания

- ✅ Работает с Microsoft Store версией Spotify
- ✅ Работает с обычной Desktop версией
- ✅ Автоматический поиск Spotify в системе
- ✅ Безопасное завершение процессов
- ✅ Мгновенный запуск воспроизведения
- ⚡ **Минимальные задержки для максимальной скорости**
