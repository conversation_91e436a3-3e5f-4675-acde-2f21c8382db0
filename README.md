# Spotify Restarter

Скрипт для автоматического перезапуска Spotify по нажатию средней кнопки мыши (Mouse 3).

## Функциональность

- Завершает все процессы Spotify.exe
- Автоматически находит и запускает Spotify заново
- Активируется по нажатию средней кнопки мыши (колесико)
- Работает в фоновом режиме

## Установка и запуск

### Способ 1: Автоматический (рекомендуется)
1. Запустите файл `run_spotify_restarter.bat`
2. Скрипт автоматически установит зависимости и запустится

### Способ 2: Ручной
1. Установите зависимости:
   ```
   pip install -r requirements.txt
   ```
2. Запустите скрипт:
   ```
   python Spotify.py
   ```

## Использование

1. Запустите скрипт
2. Нажмите среднюю кнопку мыши (колесико) в любом месте экрана
3. Spotify автоматически перезапустится
4. Для выхода из скрипта нажмите Ctrl+C

## Требования

- Python 3.6+
- Windows
- Установленный Spotify

## Зависимости

- `psutil` - для управления процессами
- `pynput` - для отслеживания нажатий мыши

## Примечания

- Скрипт автоматически ищет Spotify в стандартных местах установки
- Работает с любой версией Spotify (Desktop App)
- Безопасно завершает процессы перед перезапуском
