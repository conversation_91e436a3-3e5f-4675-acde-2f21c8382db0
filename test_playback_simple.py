import time
import win32gui
from pynput.keyboard import Key, Controller

class SimplePlaybackTester:
    def __init__(self):
        self.keyboard = Controller()
    
    def find_spotify_window_handle(self):
        """Найти handle окна Spotify"""
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_title = win32gui.GetWindowText(hwnd)
                title_lower = window_title.lower()
                # Ищем именно приложение Spotify
                if ('spotify' in title_lower and 
                    len(window_title) > 1 and 
                    not '.py' in title_lower and 
                    not 'restartspotify' in title_lower):
                    windows.append((hwnd, window_title))
            return True
        
        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        
        if windows:
            return windows[0][0], windows[0][1]
        return None, None
    
    def _press_key(self, key):
        """Нажать клавишу"""
        self.keyboard.press(key)
        time.sleep(0.05)
        self.keyboard.release(key)
    
    def _check_playback_started(self):
        """Проверить, началось ли воспроизведение"""
        try:
            hwnd, title = self.find_spotify_window_handle()
            print(f"Current window title: '{title}'")
            if title and title.strip().lower() != 'spotify free':
                # Если в заголовке есть информация о треке, вероятно играет музыка
                if len(title.strip()) > 15 and ('–' in title or '-' in title or '•' in title):
                    print("[OK] Playback detected (track info in title)")
                    return True
            print("[NO] No playback detected")
            return False
        except Exception as e:
            print(f"Error checking playback: {e}")
            return False
    
    def test_simple_methods(self):
        """Тестировать простые методы без фокусировки"""
        print("=== TESTING SIMPLE PLAYBACK METHODS ===")
        
        hwnd, title = self.find_spotify_window_handle()
        if not hwnd:
            print("Spotify window not found - make sure Spotify is running")
            return
        
        print(f"Found Spotify: {title}")
        
        # Простые методы без фокусировки
        methods = [
            ("Media Play/Pause Key", lambda: self._press_key(Key.media_play_pause)),
            ("Space Key", lambda: self._press_key(Key.space)),
        ]
        
        for i, (name, method) in enumerate(methods, 1):
            print(f"\n--- Method {i}: {name} ---")
            try:
                print("Before:")
                self._check_playback_started()
                
                print(f"Pressing {name}...")
                method()
                time.sleep(2.0)  # Даем время на реакцию
                
                print("After:")
                if self._check_playback_started():
                    print(f"[SUCCESS] {name} worked!")
                    return True
                else:
                    print(f"[FAILED] {name} didn't start playback")
                    
            except Exception as e:
                print(f"[ERROR] in {name}: {e}")
        
        print("\n=== All methods tested ===")
        return False

def main():
    print("Simple Spotify Playback Tester")
    print("Make sure Spotify is running and try playing/pausing music")
    print("This will test if our key presses work...")
    print()
    
    tester = SimplePlaybackTester()
    tester.test_simple_methods()

if __name__ == "__main__":
    main()
